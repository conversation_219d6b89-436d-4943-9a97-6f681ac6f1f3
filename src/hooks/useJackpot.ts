import { ref, computed } from 'vue'
import { useBaseStore } from '@/stores'
import { useJackpotStore } from '@/stores/jackpotStore'
import { getDaylyJackpot } from '@/api/jackpot'
import { isLoggedIn } from '@/utils'
import { ERROR_CODES } from '@/enums'

/**
 * Jackpot数据获取和处理的Hook
 */
export const useJackpot = () => {
  const baseStore = useBaseStore()
  const jackpotStore = useJackpotStore()

  const loading = ref(false)
  const error = ref<Error | null>(null)


  /**
   * 通过长连接获取Jackpot数据
   * @param loading 是否显示加载动画
   * @returns
   */
  const fetchJackpotBySocket = async (showLoading = false, showTip = false) => {
    try {
      loading.value = showLoading
      error.value = null

      const response = await baseStore.getSignEvent(
        {
          id: 'daylyJackpot',
          version: baseStore.wallet?.currency,
        },
        showLoading,
        showTip
      )
			console.log('%c------ response.acticity ','background-color:blue;font-size:12px;color:#fff', response.acticity);

      if (response?.code === ERROR_CODES.OK && response.acticity?.data) {
        const jackpotInfo = response.acticity
        jackpotStore.setJackpotData(jackpotInfo.data)
        // Socket请求中，显示Jackpot的条件是 status === 1
        jackpotStore.setShowJackpot(jackpotInfo.status === 1)
        return jackpotInfo.data
      }

      // 如果没有有效数据或请求失败，重置状态
      resetJackpotState()
      return null
    } catch (err) {
      error.value = err instanceof Error ? err : new Error('未知错误')
      resetJackpotState()
      return null
    } finally {
      loading.value = false
    }
  }

   // 通过 http 获取Jackpot数据
  const fetchJackpotByHttp = async (showLoading = false) => {
    try {
      loading.value = showLoading
      error.value = null

      const response = await getDaylyJackpot({
        id: 'daylyJackpot',
        version: '1'
      }, showLoading)

      if (response?.code === ERROR_CODES.OK && response.data) {
        jackpotStore.setJackpotData(response.data)
        // HTTP请求中，只要请求成功（code === 200）就显示Jackpot
        jackpotStore.setShowJackpot(true)
        return response.data
      }

      // 如果没有有效数据或请求失败，重置状态
      resetJackpotState()
      return null
    } catch (err) {
      console.error('获取Jackpot数据失败（HTTP）:', err)
      error.value = err instanceof Error ? err : new Error('未知错误')
      resetJackpotState()
      return null
    } finally {
      loading.value = false
    }
  }

  /**
   * 根据登录状态选择合适的请求方式获取Jackpot数据
   * @param showLoading 是否显示加载动画
   * @param showTip 是否显示错误提示（仅对socket请求有效）
   * @returns
   */
  const fetchJackpotData = async (showLoading = false, showTip = false) => {
		console.log('%c------ jackpot请求 ','background-color:blue;font-size:12px;color:#fff', );
    try {
      if (isLoggedIn()) {
        // 已登录用户使用Socket请求
        return await fetchJackpotBySocket(showLoading, showTip)
      } else {
        // 未登录用户使用HTTP请求
        return await fetchJackpotByHttp(showLoading)
      }
    } catch (err) {
      error.value = err instanceof Error ? err : new Error('未知错误')
      resetJackpotState()
      return null
    }
  }

  /**
   * 重置Jackpot状态
   */
  const resetJackpotState = () => {
    jackpotStore.setJackpotData(null)
    jackpotStore.setShowJackpot(false)
  }

  // 计算属性：是否显示Jackpot组件
  const showJackpot = computed(() => {
    return jackpotStore.showJackpot
  })

  return {
    loading,
    error,
    showJackpot,
    fetchJackpotData,
    resetJackpotState
  }
}