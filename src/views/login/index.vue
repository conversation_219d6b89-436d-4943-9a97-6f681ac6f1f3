<template>
    <div class="login">
        <div class="login-bg">
            <div class="login-header">
                <!-- <Back /> -->
                <div class="login-logo"></div>
                <div class="login-close" @click="router.back()">
                    <svg xmlns="http://www.w3.org/2000/svg" width="36" height="34" viewBox="0 0 56 54" fill="none">
                        <rect width="56" height="54" rx="12" fill="#3A3F51"></rect>
                        <path
                            d="M40.838 14.5016C41.6678 15.3314 41.6677 16.6769 40.838 17.5068L31.1788 27.166L40.838 36.8252C41.6678 37.6549 41.6676 39.0005 40.838 39.8304C40.0081 40.6602 38.6626 40.6602 37.8328 39.8304L28.2544 30.252L18.676 39.8304C17.8461 40.6602 16.5007 40.6602 15.6708 39.8304C14.8412 39.0005 14.841 37.6549 15.6708 36.8252L25.3293 27.1667L15.6701 17.5075C14.8407 16.6777 14.8407 15.332 15.6701 14.5023C16.4999 13.6725 17.8454 13.6727 18.6753 14.5023L28.2537 24.0807L37.8328 14.5016C38.6626 13.6719 40.0082 13.6719 40.838 14.5016Z"
                            fill="#8EAABA"
                        ></path>
                    </svg>
                </div>
            </div>

            <div class="login-promo">
                <p class="logo-tip">Secure and Fast</p>
                <p class="login-signup">Sign up</p>
                <p class="login-gifts">Get <span class="text-yellow">₱100 gift</span></p>
            </div>

            <div class="login-title"><p class="login-text">Sign in</p></div>
            <div class="login-input">
                <div class="login-row">
                    <div class="input-left area-code">
                        <CountyMobile @countryCodeChange="getCountrycode" />
                    </div>
                    <div class="input-right">
                        <van-field
                            v-model="phoneNum"
                            type="digit"
                            :placeholder="$t('Enter_mobile_number')"
                            :maxlength="15"
                            @change="codeOpt.times = 0"
                        />
                    </div>
                </div>
                <div class="login-row">
                    <div class="msg-code"></div>
                    <div class="input-right otp-input-right">
                        <van-field v-model="msgCode" type="digit" :placeholder="$t('Enter_verification_code')" :maxlength="4" />
                        <div class="msg-line"></div>
                        <div class="send-otp-btn" @click="sendMsg">
                            <div :class="['w-full', { 'opacity-50': codeOpt.timer }]" v-if="codeOpt.isSend">
                                <span v-if="codeOpt.timer">({{ codeOpt.seconds }}s)</span>
                                <span v-else>Resend</span>
                            </div>
                            <div :class="phoneNum ? 'opacity-100' : 'opacity-40'" v-else>Send OTP</div>
                        </div>
                    </div>
                </div>

                <div class="login-gift">
                    <div class="login-gift-title">{{ $t('login.login_select_game_title') }}</div>
                    <div class="gift-list">
                        <ul class="select-list">
                            <li
                                :class="['list-item', { active: selectGift === index }, { status: item.status !== 1 }]"
                                v-for="(item, index) in giftconfigs"
                                :key="item.id"
                                @click="selectGift = index"
                            >
                                <!-- {{ item.name }} -->
                                <img :src="item.loginimg" />
                                <div class="list-item-tab">
                                    <div class="list-item-tab-num">
                                        {{ curSymbol[wallet?.currency] }}
                                        <p class="responsive-text">{{ item.worth * giftList[index].num }}</p>
                                    </div>
                                    <div class="list-item-tab-name">
                                        <p class="responsive-text">{{ item.name }}</p>
                                    </div>
                                    <div :class="['list-item-tab-status', { selected: selectGift == index }]">
                                        <div v-if="selectGift == index">{{ $t('Active') }}</div>
                                        <div v-else>{{ $t('Apply') }}</div>
                                    </div>
                                </div>
                            </li>
                        </ul>
                    </div>
                </div>
                <!-- <div>
                    <div class="login-btn" @click="goRegister">
                        <Button type="primary"> {{ $t('Register_button') }} </Button>
                    </div>
                </div> -->
                <!-- <div class="login-btn guset-btn">
                <Button type="primary" @confirm="gusetLogin">{{ $t('Visitor') }}</Button>
            </div> -->
                <div class="login-statement" @click="goProtoPage" v-html="$t('Login_bottom_agreeme')"></div>
                <div class="login-btn">
                    <Button class="primary" :disabled="!phoneNum || !msgCode" @click="doSign"> {{ $t('Login_button') }}</Button>
                </div>
            </div>

            <van-divider class="login-divider"> {{ $t('Or sign in with') }} </van-divider>
            <div v-if="store.ntvfrom === 'selfapk'" class="third-login">
                <div>
                    <div class="third-item1 third-item1-apk" v-for="item in thirdApkConfig" :key="item.type" @click="thirtLogin(item)">
                        <img class="w-full h-full" :src="item.icon" />
                    </div>
                </div>
            </div>
            <div v-else class="third-login">
                <div class="third-item1" v-for="item in thirdConfig" :key="item.type" @click="thirtLogin(item)">
                    <img class="w-full h-full" :src="item.icon" />
                </div>
                <div id="google-signin-button" class="third-item"></div>
                <p class="login-version">APP: {{ appVersion }}</p>
            </div>

            <van-floating-bubble v-if="isDev" axis="xy" icon="chat" magnetic="x" @click="gusetLogin" />
            <div class="login-service" @click="goService"></div>
        </div>
    </div>
</template>
<script setup lang="ts" name="login">
import * as service from '@/api'
import { useBaseStore } from '@/stores'
import Button from '@/components/Button.vue'
import { useCutdown } from '@/hooks/useCutdown'
import { H5Login } from '@/utils/H5Login'
import eventbus from '@/utils/bus'
import md5 from 'md5'
import CountyMobile from '@/components/countyMobile.vue'
import { useRouter, useRoute } from 'vue-router'
import { encode } from 'js-base64'
import { isValidPhoneNumber } from 'libphonenumber-js'
import { useI18n } from 'vue-i18n'
import qs from 'qs'
import { getUrlParams } from '@/utils'
import { CustomerUrl } from '@/enums'
import { SAJSFacebookLogin, SAJSGoogleLogin } from '@/components/common/SAJSBrige'
import { getFreeGamesConfig } from '@/api/index'
import { storeToRefs } from 'pinia'

const { setToken, setNewUser } = useBaseStore()
const store = useBaseStore()
const router = useRouter()
const route = useRoute()
const i18n = useI18n()
const path = '../../assets/img/security/'
const file = import.meta.glob('../../assets/img/security/*', { eager: true })
const { curSymbol, wallet } = storeToRefs(store)

const giftList = ref([])
const giftconfigs = ref([])
const selectGift = ref(0)

const useFor = computed(() => {
    return giftconfigs.value[selectGift.value].useFor
})

const waitForGoogleButton = function () {
    const observer = new MutationObserver((mutations) => {
        const googleButton = document.getElementById('google-signin-button')
        // 检查按钮是否已渲染（比如内部是否有子元素）
        if (googleButton && googleButton.children.length > 0) {
            setThirdItemSize()
            observer.disconnect() // 停止监听
        }
    })

    // 监听 #google-signin-button 的子元素变化
    const config = { childList: true, subtree: true }
    const googleButtonContainer = document.getElementById('google-signin-button')
    if (googleButtonContainer) {
        observer.observe(googleButtonContainer, config)
    }
}

const setThirdItemSize = function () {
    const googleButton = document.getElementById('google-signin-button')
    if (googleButton) {
        const width = googleButton.offsetWidth
        const height = googleButton.offsetHeight
        document.querySelectorAll('.third-item1').forEach((item) => {
            item.style.width = `${width}px`
            item.style.height = `${height}px`
        })
    }
}

const getImg = (name) => {
    return file[path + name].default
}

const trackerName = ref('')
const appVersion = ref(__APP_VERSION__)

const isDev = computed(() => import.meta.env.VITE_ENV_NAME !== 'production') //lzj 正式版先开放游客登录，用来测试 上线需修改

// let linkId = localStorage.getItem('__rb_4475304769_link_id') || ''
// if (linkId) {
//     linkId = JSON.parse(linkId)
// }

let linkId = store.link_id

const isNotSelf = !(localStorage.getItem('is-self') || '')

const trackerToken = ref('')
if (!isNotSelf) {
    trackerToken.value = getUrlParams('fbclid') || getUrlParams('ttclid')
}
const { codeOpt, doCutDown } = useCutdown()
const areaCode = ref('')
const phoneNum = ref('')
const msgCode = ref('')
// 不再需要thirdConfig，直接使用Google和Facebook的原生按钮
const thirdConfig = [
    // {
    //     type: 'google',
    //     icon: getImg('google.png'),
    // },
    {
        type: 'faceBook',
        icon: getImg('facebook.avif'),
    },
]

const thirdApkConfig = [
    {
        type: 'google',
        icon: getImg('googleapk.png'),
    },
    {
        type: 'faceBook',
        icon: getImg('facebook.avif'),
    },
]

const thirtLogin = (item) => {
    if (store.ntvfrom === 'selfapk') {
        if (item.type === 'google') {
            SAJSGoogleLogin()
        } else {
            SAJSFacebookLogin()
        }
    } else if (item.type === 'google') {
        H5Login.enableGoogleSignInPrompt()
    } else {
        H5Login.loginWithFacebook()
    }
}

// 直接使用Facebook登录方法
const loginWithFacebook = () => {
    H5Login.loginWithFacebook()
}

const getCountrycode = (code) => {
    areaCode.value = code
}
const goService = () => {
    var url = CustomerUrl
    if (store.partner == 10002000) {
        url = 'https://www.facebook.com/profile.php?id=61552549876486'
    }
    window.open(url)
}
const reset = () => {
    phoneNum.value = ''
    msgCode.value = ''
}

const sendMsg = () => {
    if (!phoneNum.value) {
        return
    }
    // @ts-ignore
    if (!isValidPhoneNumber(phoneNum.value, areaCode.value)) {
        return showToast(i18n.t('Please_enter_a_valid'))
    }
    if (codeOpt.timer) {
        return
    }
    codeOpt.times = codeOpt.times + 1

    service
        .getSms({
            apptitle: 'h5',
            country: areaCode.value,
            grant: 'authorize',
            sendnum: codeOpt.times,
            sign: md5(phoneNum.value),
            telephone: phoneNum.value,
        })
        .then((res) => {
            if (res.code === 200) {
                codeOpt.isSend = true
                doCutDown()
                showToast(i18n.t('Verification_code_ha'))
            } else {
                showToast(i18n.t('Send_failed_wait'))
            }
        })
}
const doSign = async () => {
    if (!phoneNum.value) {
        return showToast(i18n.t('Please_enter_a_valid'))
    }
    // @ts-ignore
    if (!isValidPhoneNumber(phoneNum.value, areaCode.value)) {
        return showToast(i18n.t('Please_enter_a_valid'))
    }
    if (!/\d{4}/.test(`${msgCode.value}`)) {
        return showToast(i18n.t('Incorrect_verification'))
    }
    getExtention()
    try {
        const res = await service.mobileLogin(
            {
                userinfo: {
                    partner: store.partner,
                    telcontry: 'null',
                    telephone: phoneNum.value,
                    code: msgCode.value,
                    verificode: null,
                    grant: 'authorize',
                    country: areaCode.value,
                    linkId,
                    itemuserfor: useFor.value,
                },
            },
            { trackerName: trackerName.value }
        )
        if (res.code === 200) {
            reset()
            handleLogin(res.token, res.newuser)
        }
        // else {
        //     showToast(i18n.t('Login_failed_please'))
        // }
    } catch (e) {
        showToast(i18n.t('Login_failed_please'))
    }
}
const gusetLogin = async () => {
    const time = Date.now().toString()
    const param = {
        openid: 'bydtd1Y54fsdfSDFSDDlfd1673dVaQfA' + time,
        nickname: '',
        sex: 1,
        language: 'YD',
        city: '',
        province: '',
        country: 'YD',
        headimgurl: '',
        privilege: [],
        unionid: 'oVmKOw36JEGiBVXzyw__lww1Sdfs1691646592917' + time,
        partner: store.partner,
        telcontry: '',
        linkId,
        itemuserfor: useFor.value,
    }
    // param.headimgurl = 'http://update.weegames.cn/ava/' + Math.floor(Math.random() * 20 + 1) + '.jpg'
    getExtention()
    try {
        const res = await service.guestLogin({ wechatData: param }, { trackerName: trackerName.value, trackerToken: trackerToken.value })
        if (res.code === 200) {
            reset()
            handleLogin(res.token, res.newuser)
        }
        // else {
        //     showToast(i18n.t('Login_failed_please'))
        // }
    } catch (e) {
        showToast(i18n.t('Login_failed_please'))
    }
}
function loginCallback(data: LoginData) {
    if (route.path !== '/login') {
        return
    }
    getExtention()
    if (data.from == 'facebook') {
        C2S_FacebookLogin(data)
    } else if (data.from == 'google') {
        C2S_GoogleLogin(data)
    }
}
// facebook 登录
const C2S_FacebookLogin = async (fbinfo) => {
    console.log('fbinfo-------', fbinfo)

    if (fbinfo.status == 'failed') {
        return console.log('facebook 登录失败')
    }

    if (typeof fbinfo == 'string' && store.ntvfrom === 'selfapk') {
        fbinfo = JSON.parse(fbinfo)
    }
    const param = {
        partner: store.partner,
        telcontry: '',
        unionid: fbinfo.id,
        nickname: encode(fbinfo.name),
        accesstoken: fbinfo.accesstoken,
        deviceid: '',
        verificode: '',
        avatar: 'https://graph.facebook.com/' + fbinfo.id + '/picture?type=large', //fbinfo.picture.data.url
        linkId,
        itemuserfor: useFor.value,
    }

    try {
        const fbResp = await service.facebookLogin({ userinfo: param }, { trackerName: trackerName.value, trackerToken: trackerToken.value })
        if (fbResp.code === 200) {
            handleLogin(fbResp.token, fbResp.newuser)
        } else {
            showToast(i18n.t('Login_failed_please'))
        }
    } catch (e) {
        showToast(i18n.t('Login_failed_please'))
    }
}
// goole 登录
const C2S_GoogleLogin = async (googleInfo) => {
    console.log('googleInfo---q----', googleInfo)
    if (googleInfo == 'failed') {
        return
    }
    if (typeof googleInfo == 'string' && store.ntvfrom === 'selfapk') {
        googleInfo = JSON.parse(googleInfo)
    }
    console.log('googleInfo-------', googleInfo.id)

    const param = {
        partner: store.partner,
        unionid: googleInfo.id,
        nickname: encode(googleInfo.name),
        telcontry: '',
        deviceid: '',
        accesstoken: googleInfo.accesstoken,
        clientid: googleInfo.client_id,
        verificode: '',
        avatar: googleInfo.avatar,
        linkId,
        itemuserfor: useFor.value,
    }
    try {
        const ggResp = await service.googleLogin({ userinfo: param }, { trackerName: trackerName.value, trackerToken: trackerToken.value })
        if (ggResp.code === 200) {
            handleLogin(ggResp.token, ggResp.newuser)
        } else {
            showToast(i18n.t('Login_failed_please'))
        }
    } catch (e) {
        showToast(i18n.t('Login_failed_please'))
    }
}
// 获取到token统一处理登录
const handleLogin = (token, isNew = false) => {
    setToken(token)
    setNewUser(isNew)
    if (route.query.path) {
        router.replace({
            path: route.query.path,
            query: {
                back: true,
                ...(route.query.params ? qs.parse(route.query.params) : {}),
            },
        })
        return
    }
    router.replace('/')
}
const goProtoPage = (e) => {
    const classList = e.target.classList
    if (classList.contains('service')) {
        window.open('https://betfugu.com/active/ToS.html')
    }
    if (classList.contains('policy')) {
        window.open('https://betfugu.com/active/privacy.html')
    }
}
// const getPixelName = async () => {
//     return new Promise((resolve) => {
//         // 替换为linkid
//         const pixelName = localStorage.getItem('pwa-link-id')
//         resolve(pixelName ? pixelName.split('x')[0] + 'pwa' : pixelName)
//     })
// }
// const getExtention = () => {
//     // if (!isNotSelf) {
//     //     // return getPixelName().then((pixelName) => {
//     //     //     // trackerName.value = pixelName as string
//     //     //     trackerName.value = 'P' + (1000000 + Number(pixelName))
//     //     // })
//     //     return localStorage.getItem('trackerName') || ''
//     // }
//     return linkId
//         ? getExtentionId({
//               link_id: linkId,
//           })
//               .then((res) => {
//                   if (res.code === 0) {
//                       trackerName.value = res.data['promote_url_id']
//                   }
//               })
//               .catch((e) => {
//                   console.log(e)
//               })
//         : ''
// }

const getExtention = () => {
    trackerName.value = store.promote_url_id
}

const getFreeGamesCfg = async () => {
    try {
        const res = await getFreeGamesConfig()
        console.log('res---', res)
        if (res.code === 200) {
            giftList.value = res.gifts?.items
            giftconfigs.value = res.confs
        }
    } catch (e) {
        return []
    }
}

onMounted(() => {
    //获取免费游戏配置
    getFreeGamesCfg()

    // 初始化Google登录按钮
    H5Login.init()

    // 监听登录回调
    eventbus.on(H5Login.H5_Login_Back, loginCallback)
    eventbus.on(H5Login.GOOGLE_USER_LOGIN, C2S_GoogleLogin)
    eventbus.on(H5Login.FB_USER_LOGIN, C2S_FacebookLogin)
    waitForGoogleButton()
    setThirdItemSize()
})

onBeforeUnmount(() => {
    eventbus.off(H5Login.H5_Login_Back, loginCallback)
    eventbus.off(H5Login.GOOGLE_USER_LOGIN, C2S_GoogleLogin)
    eventbus.off(H5Login.FB_USER_LOGIN, C2S_FacebookLogin)
})

// const store = useBaseStore()
</script>
<style scoped lang="scss">
.login {
    position: fixed;
    top: calc(var(--safe-height) + constant(safe-area-inset-top));
    top: calc(var(--safe-height) + env(safe-area-inset-top));
    left: 0;
    width: 100%;
    height: calc(var(--vh, 1vh) * 100) !important;

    overflow: auto;
    padding: 0;
    background: #1c1c27;
    background-size: 100% auto;

    .login-bg {
        width: 100%;
        min-height: 100%;
        background: url('@/assets/img/login/login_head_bg.png') no-repeat left top/100% 525px;
        background-size: 100% auto;
        // -webkit-mask-image: linear-gradient(to bottom, rgba(0, 0, 0, 1) 0%, rgba(0, 0, 0, 1) 70%, rgba(0, 0, 0, 0) 100%);
        // mask-image: linear-gradient(to bottom, rgba(0, 0, 0, 1) 10%, rgba(0, 0, 0, 1) 20%, rgba(0, 0, 0, 0) 100%);
        .login-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            padding: 30px 45px 0;
            width: 100%;
            position: relative;
            z-index: 1;
        }

        .login-logo {
            width: 283px;
            height: 73px;
            background: url('@/assets/img/login/logo.png') no-repeat center;
            background-size: contain;
            //margin-left: 0; /* 移除负边距，使其与下方输入框对齐 */
        }

        .login-close {
            // margin-top: 20px;

            position: absolute;
            top: 30px;
            right: 30px;
        }

        .login-promo {
            margin-top: 0px;
            margin-bottom: 20px;
            padding: 0px 45px;
            color: white;
            position: relative;
            z-index: 1;

            .logo-tip {
                font-size: 36px;
                opacity: 0.7;
                color: #788094;
                font-weight: 600;
            }

            .login-signup {
                font-size: 60px;
                font-weight: bold;
                margin-top: 80px;
                font-family: 'Helvet';
            }

            .login-gifts {
                font-size: 42px;
                margin-top: -10px;
                font-weight: 600;
                .text-yellow {
                    color: #ffde00;
                    font-weight: bold;
                }
            }
        }

        .login-text {
            // line-height: 150px;
            font-size: 36px;
            color: #9da3b4;
            padding: 0 45px;
            font-weight: normal;
        }
        .login-service {
            width: 80px;
            height: 80px;
            background: url('@/assets/img/login/service_new.png') no-repeat;
            background-size: 100% 100%;
            position: fixed;
            right: 40px;
            bottom: 140px;
            z-index: 10;
        }
    }
}
.login-picker {
}
.login-title {
    color: white;
    font-size: 36px;
    font-weight: bold;
    margin-top: 60px;
    text-align: left;
}

.login-row {
    @apply flex;
    font-size: 30px;
    margin: 10px 0px 15px;
    border-radius: 16px;
    background-color: #2a2d3d;
    overflow: hidden;

    .input-left {
        width: 150px;
        height: 90px;
        background: transparent;
        color: #fff;
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;

        &:after {
            content: '';
            position: absolute;
            right: 0;
            top: 25%;
            height: 55%;
            width: 3px;
            background-color: #76767f;
        }
    }
    .area-code {
        @apply flex items-center justify-center;
    }
    .input-right {
        flex: 1;
        height: 90px;
        background-color: transparent;
        color: #fff;
        overflow: hidden;
        position: relative;

        :deep(.van-field) {
            height: 100%;
            background-color: transparent;
            padding: 0 15px;

            .van-field__body {
                height: 100%;
            }
            .van-field__control {
                color: #fff;
                &::placeholder {
                    color: rgba(255, 255, 255, 0.5);
                }
            }
        }
    }
    .msg-code {
        @apply flex items-center text-center;
        padding: 0 15px;

        color: rgba(255, 255, 255, 0.4);
    }
    .msg-line {
        &:after {
            content: '';
            position: absolute;
            top: 25%;
            height: 55%;
            width: 3px;
            background-color: #76767f;
        }
    }

    .otp-input-right {
        display: flex;
        align-items: center;

        :deep(.van-field) {
            flex: 1;

            .van-field__body {
                height: 100%;
            }
            .van-field__control {
                color: #fff;
                &::placeholder {
                    color: rgba(255, 255, 255, 0.5);
                }
            }

            // 去掉下划线
            &::after {
                border-bottom: none;
            }
        }

        .send-otp-btn {
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            color: rgba(255, 255, 255, 0.8);
            white-space: nowrap;
            cursor: pointer;
        }
    }
}
.login-btn {
    // margin-top: 20px;
    // padding: 0;
    // width: 100%;
    // background-color: violet;
    height: 100px;
    margin-bottom: 10px;

    .primary {
        // transform: translate(0, -60%);
        border-radius: 10px;
        height: 80px;
        font-size: 32px;
        font-weight: bold;
        width: 100%;
        border: none;
        background: linear-gradient(90deg, #2aee88 0%, #9ae871 100%), linear-gradient(#8b0500, #8b0500);
    }
}

.guset-btn {
    margin-top: 30px;
}
.third-login {
    display: flex !important;
    flex-direction: column !important;
    align-items: center !important;
    justify-content: flex-start !important;
    gap: 20px;
    width: 100%;
    height: 300px;

    .third-item1 {
        width: 500px;
        height: 100px;
        &.third-item1-apk {
            padding-bottom: 20px !important;
        }
    }
}

.login-divider {
    color: #fff;
    font-size: 30px;
    padding: auto;
}

.login-statement {
    margin-top: 20px;
    // margin-bottom: 30px;
    // padding: 30px;
    font-size: 24px;
    line-height: 52px;
    color: #5e5955;
    text-align: center;

    :deep(.state-notice) {
        color: #fff;
    }
}
.login-version {
    color: rgba(255, 255, 255, 0.8);
    text-align: center;
    font-size: 24px;
    margin: 30px 0;
}

.login-input {
    padding: 0px 45px 0;
}
.login-gift {
    .login-gift-title {
        // text-align: center;
        color: #9da3b4;
        font-size: 33px;
        padding: 10px 0;
        // font-weight: bold;
    }
    .gift-list {
        width: 100%;
        height: 142px;
        .select-list {
            @apply grid grid-cols-2 gap-[30px];

            .list-item {
                display: flex;
                justify-content: center;
                align-items: center;
                position: relative;
                width: 320px;
                height: 142px;
                aspect-ratio: 2 / 1;
                // line-height: 100px;
                // background-image: linear-gradient(#444b5e, #444b5e), linear-gradient(#ffffff, #ffffff);
                // background-color: #1e8597aa;
                background-blend-mode: normal, normal;
                border-radius: 15px;
                border: solid 6px #484e50;
                text-align: center;
                font-size: 42px;
                font-weight: 400;
                color: #fff;

                span {
                    margin-left: 2px;
                    color: #ffa72a;
                }

                &.active {
                    border-color: #f1a940;
                    background-color: #5eca9c33;

                    // &::after {
                    //     position: absolute;
                    //     right: -5px;
                    //     // bottom: -4px;
                    //     display: inline-block;
                    //     content: '';
                    //     width: 52px;
                    //     height: 52px;
                    //     // background: url('@/assets/img/wallets/checked.png') no-repeat;
                    //     background-size: 100% 100%;
                    // }
                }

                img {
                    width: 100%;
                    height: 100%;
                }

                .list-item-tab {
                    position: absolute;
                    width: 100%;
                    height: 50px;
                    background-color: #1c1b28bb;
                    bottom: 0;
                    border-radius: 0 0 10px 10px;
                    font-size: 24px;
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    .list-item-tab-num {
                        width: 60px;
                        display: flex;
                        text-align: left;
                        color: #93e8aa;
                        font-weight: 400;
                        font-size: 28px;
                        .responsive-text {
                            font-weight: bold;
                            font-family: 'Courier New', Courier, monospace;
                            display: flex;
                            align-items: end;
                            font-size: 24px;
                        }
                    }
                    .list-item-tab-name {
                        width: 100%;
                        max-width: 150px; /* 设置容器最大宽度 */
                        text-align: left;
                        .responsive-text {
                            font-weight: 500;
                            word-break: break-word;
                            white-space: nowrap;
                            overflow: hidden;
                            text-overflow: ellipsis;
                        }
                    }
                    .list-item-tab-status {
                        width: 90px;
                        height: 50px;
                        display: flex;
                        justify-content: center;
                        align-items: center;
                        background: linear-gradient(90deg, #50afbf 0%, #5cc89b 100%), linear-gradient(#8b0500, #8b0500);
                        font-size: 24px;
                        border-radius: 0 0 9px 8px;
                        font-weight: 600;
                        // color: #fffa;
                        &.selected {
                            background: linear-gradient(90deg, #f1a940 0%, #f1a940 100%), linear-gradient(#8b0500, #8b0500);
                            color: #000;
                        }
                    }
                }
            }
        }
    }
}
</style>
