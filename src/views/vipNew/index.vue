<template>
    <div class="menu">
        <div class="menu-title">
            <button class="menu-close" @click="goBack">
                <img src="@/assets/img/new-home/newsetting/back.svg" alt="x" />
            </button>
            <div class="nav-title">VIP Club</div>
        </div>
        <div ref="homeScroll" class="scroll-container" @scroll="handleScroll" :class="[`vip-card-bg${getVipBadge(vipInfo.curLevel)}`]">
            <div class="vip-content">
                <VipHeaderOptimized />
                <VIPBenefitsLarger />
            </div>
        </div>
    </div>
</template>
<script lang="ts" setup>
import VipHeaderOptimized from '@/components/vip/VipHeaderOptimized.vue'
import VIPBenefitsLarger from '@/components/vip/VIPBenefitsLarger.vue'
import { useScroll } from '@/hooks/useScroll'
import { useBaseStore } from '@/stores'
import { storeToRefs } from 'pinia'
import { useRouter } from 'vue-router'
const router = useRouter()
const store = useBaseStore()
const { vipInfo } = storeToRefs(store)
// const { handleScroll } = useScroll()

const homeScroll = ref(null)

const goBack = () => router.back()

const getVipBadge = (level: number) => {
    var index = 0
    if (level >= 1 && level <= 3) {
        index = 1
    } else if (level >= 4 && level <= 6) {
        index = 2
    } else if (level >= 7 && level <= 9) {
        index = 3
    } else if (level >= 10 && level <= 11) {
        index = 4
    } else if (level >= 12) {
        index = 5
    }
    return index
}

const handleScroll = (event) => {
    const menutitle = document.querySelector('.menu-title') as HTMLElement
    if (event.target.scrollTop > 50) {
        // 滚动超过 50px 时开始变化
        const opacity = Math.min(event.target.scrollTop / 200, 1) // 计算透明度（0~0.8）
        menutitle.style.backgroundColor = `rgba(50, 55, 56, ${opacity})`
    } else {
        menutitle.style.backgroundColor = 'rgba(50, 55, 56, 0)' // 回到透明
    }
}
</script>

<style lang="scss" scoped>
@use '../../components/vip/vip.scss' as *;
@use 'sass:map';
$padTop: 32px;
$titleH: 40px;
$mb: 40px;
.menu {
    width: 100%;
    height: calc(var(--vh, 1vh) * 100);
    background-size: 120% auto;
    background-position: -120px 0;
    background-color: rgba(25, 189, 189, 0);
}
.menu-title {
    @apply flex justify-center absolute;
    width: 100%;
    height: var(--menu-header);
    z-index: 1000;
    background-color: rgba(50, 55, 56, 0);
    color: #fff;
    display: flex;
    text-align: center;
    align-items: flex-end;
    padding-bottom: 30px;
    .menu-close {
        position: absolute;
        left: 25px;
        width: 55px;
        height: 55px;
        border-radius: 10px;
        background-color: #464f50;
        color: #fff;
        background-size: 100% 100%;
        justify-content: center;
        align-items: center;
        display: flex;
        :deep(img) {
            width: 100%;
            height: 60%;
        }
    }

    .nav-title {
        font-size: 30px;
        font-weight: 450;
        margin-top: 10px;
    }
}

.scroll-container {
    padding: 10px;
    background: #232626;
    height: calc(var(--vh, 1vh) * 100);
    overflow-y: auto;

    .vip-content {
        padding-top: $titleH;
    }

    @for $i from 0 through 5 {
        &.vip-card-bg#{$i} {
            background-image: linear-gradient(20deg, rgba(50, 55, 56, 255) 60%, map.get(map.get($vip-list, $i), color));
            background-blend-mode: normal, normal;
        }
    }
}
</style>
